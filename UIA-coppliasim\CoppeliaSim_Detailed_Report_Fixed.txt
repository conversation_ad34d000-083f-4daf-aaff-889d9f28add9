=== CoppeliaSim 详细操作报告 ===
生成时间: 2025-07-31 08:59:27
总操作数量: 14

=== 详细操作记录 ===
=== CoppeliaSim 操作监控日志 ===
监控开始时间: 2025-07-31 08:57:46

时间: 08:57:46
操作: 系统
元素: 监控系统
脚本: -- 操作记录
详情: 监控系统初始化成功
---
时间: 08:57:48
操作: 系统
元素: 监控系统
脚本: -- 操作记录
详情: 事件监控已启动
---
时间: 08:57:48
操作: 系统
元素: 监控系统
脚本: -- 操作记录
详情: 开始监控 CoppeliaSim 操作
---
时间: 08:58:00
操作: 状态变化
元素: 仿真开始
脚本: sim.startSimulation() -- 开始仿真
详情: 仿真状态从停止变为运行
---
时间: 08:58:24
操作: 状态变化
元素: 仿真停止
脚本: sim.stopSimulation() -- 停止仿真
详情: 仿真状态从运行变为停止
---
时间: 08:58:30
操作: 鼠标点击
元素: 查看
脚本: -- 菜单操作: 查看
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(254,27)
---
时间: 08:58:32
操作: 状态变化
元素: 仿真开始
脚本: sim.startSimulation() -- 开始仿真
详情: 仿真状态从停止变为运行
---
时间: 08:58:49
操作: 鼠标点击
元素: 编辑
脚本: -- 菜单操作: 编辑
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(115,18)
---
时间: 08:58:57
操作: 鼠标点击
元素: 选择
脚本: sim.getObjectSelection() -- 获取选中对象
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(192,29)
---
时间: 08:58:58
操作: 鼠标点击
元素: 查看
脚本: -- 菜单操作: 查看
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(258,25)
---
时间: 08:59:00
操作: 鼠标点击
元素: 工具
脚本: -- 菜单操作: 工具
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(372,25)
---
时间: 08:59:03
操作: 鼠标点击
元素: 插件
脚本: sim.loadPlugin('pluginName') -- 加载插件
详情: 类型:菜单项目 | 模式:Invoke,ScrollItem,ExpandCollapse | 位置:(417,22)
---
时间: 08:59:08
操作: 状态变化
元素: 仿真停止
脚本: sim.stopSimulation() -- 停止仿真
详情: 仿真状态从运行变为停止
---
时间: 08:59:12
操作: 鼠标点击
元素: Monitor_coppliasim_simple.ahk
脚本: -- 操作: Monitor_coppliasim_simple.ahk
详情: 类型:选择项目 | 模式:SelectionItem,ScrollItem | 位置:(475,38)
---

注意: 此文件已修复为UTF-8编码格式，中文字符现在应该能正确显示。
原始乱码文件已备份为: CoppeliaSim_Detailed_Report_20250731_085927_backup_original.txt
