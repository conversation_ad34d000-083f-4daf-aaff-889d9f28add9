#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
通用桌面应用程序UI控件检查器
=============================================================================
功能：
1. 获取任意桌面应用程序的完整控件树结构
2. 支持多种查找方式：窗口标题、进程名、类名
3. 详细显示每个控件的属性信息
4. 支持实时刷新和导出功能
5. 提供交互式界面选择目标应用程序

使用方法：
1. 运行脚本
2. 选择目标应用程序
3. 点击"扫描控件"获取完整控件树
4. 查看详细信息并可导出结果

热键：
F1  - 刷新应用程序列表
F2  - 扫描选中应用程序的控件
F3  - 导出控件信息
F4  - 清空结果
F12 - 退出程序
=============================================================================
*/

; 全局变量
global mainGui := ""
global appListView := ""
global resultEdit := ""
global statusText := ""
global currentApp := ""
global scanResults := ""

; 初始化主界面
InitializeMainInterface()

; 设置热键
F1::RefreshAppList()
F2::ScanSelectedApp()
F3::ExportResults()
F4::ClearResults()
F12::ExitApp

; 初始化主界面
InitializeMainInterface() {
    global mainGui, appListView, resultEdit, statusText
    
    ; 创建主窗口
    mainGui := Gui("+Resize +MinSize800x600", "通用UI控件检查器 - Universal UI Inspector")
    mainGui.SetFont("s9", "Microsoft YaHei")
    
    ; 顶部控制区域
    mainGui.Add("Text", "x10 y10", "选择目标应用程序:")
    
    ; 应用程序列表
    appListView := mainGui.Add("ListView", "x10 y35 w480 h200 +Grid", ["进程名", "窗口标题", "PID", "类名"])
    appListView.OnEvent("DoubleClick", (*) => ScanSelectedApp())
    
    ; 控制按钮
    mainGui.Add("Button", "x400 y35 w100 h30", "刷新列表").OnEvent("Click", (*) => RefreshAppList())
    mainGui.Add("Button", "x400 y75 w100 h30", "扫描控件").OnEvent("Click", (*) => ScanSelectedApp())
    mainGui.Add("Button", "x400 y115 w100 h30", "导出结果").OnEvent("Click", (*) => ExportResults())
    mainGui.Add("Button", "x400 y155 w100 h30", "清空结果").OnEvent("Click", (*) => ClearResults())

    ; 高级功能按钮
    mainGui.Add("Button", "x510 y35 w100 h30", "搜索控件").OnEvent("Click", (*) => SearchControls())
    mainGui.Add("Button", "x510 y75 w100 h30", "实时监控").OnEvent("Click", (*) => StartRealTimeMonitoring())
    mainGui.Add("Button", "x510 y115 w100 h30", "统计报告").OnEvent("Click", (*) => GenerateStatisticsReport())
    
    ; 扫描选项
    mainGui.Add("GroupBox", "x500 y155 w110 h80", "扫描选项")
    depthCombo := mainGui.Add("ComboBox", "x510 y175 w90", ["浅层(3级)", "中等(5级)", "深层(8级)", "完整(无限)"])
    depthCombo.Choose(2) ; 默认选择中等深度
    depthCombo.Name := "DepthCombo"

    ; 过滤选项
    filterCheck := mainGui.Add("CheckBox", "x510 y200 w90", "过滤系统控件")
    filterCheck.Value := 1 ; 默认启用过滤
    filterCheck.Name := "FilterCheck"
    
    ; 结果显示区域
    mainGui.Add("Text", "x10 y250", "控件树结构:")
    resultEdit := mainGui.Add("Edit", "x10 y270 w800 h280 +VScroll +HScroll +ReadOnly", "请选择应用程序并点击'扫描控件'开始分析...")
    resultEdit.SetFont("s8", "Consolas")

    ; 状态栏
    statusText := mainGui.Add("Text", "x10 y560 w800", "就绪 | 热键: F1-刷新 F2-扫描 F3-导出 F4-清空 F5-搜索 F6-监控 F7-统计 F12-退出")
    
    ; 窗口事件
    mainGui.OnEvent("Close", (*) => ExitApp)
    mainGui.OnEvent("Size", ResizeControls)
    
    ; 显示界面
    mainGui.Show("w820 h590")
    
    ; 初始加载应用程序列表
    RefreshAppList()
}

; 窗口大小调整事件
ResizeControls(GuiObj, MinMax, Width, Height) {
    global appListView, resultEdit, statusText
    
    if MinMax = -1 ; 最小化时不处理
        return
    
    try {
        ; 调整控件大小
        appListView.Move(, , Width - 340, )
        resultEdit.Move(, , Width - 30, Height - 320)
        statusText.Move(, Height - 30, Width - 20, )
    } catch {
        ; 忽略调整错误
    }
}

; 刷新应用程序列表
RefreshAppList() {
    global appListView, statusText
    
    statusText.Text := "正在刷新应用程序列表..."
    
    ; 清空现有列表
    appListView.Delete()
    
    try {
        ; 获取所有可见窗口
        windows := []
        
        ; 枚举所有顶级窗口
        windowList := WinGetList()

        for hwnd in windowList {
            try {
                if !hwnd
                    continue
                
                ; 检查窗口是否可见且有标题
                if !WinGetTitle(hwnd) || !IsWindow(hwnd)
                    continue
                
                ; 获取窗口信息
                title := WinGetTitle(hwnd)
                processName := WinGetProcessName(hwnd)
                pid := WinGetPID(hwnd)
                className := WinGetClass(hwnd)
                
                ; 过滤掉一些系统窗口
                if IsValidWindow(title, processName, className) {
                    windows.Push({
                        ProcessName: processName,
                        Title: title,
                        PID: pid,
                        ClassName: className,
                        HWND: hwnd
                    })
                }
                
            } catch {
                continue
            }
        }
        
        ; 按进程名排序
        windows := SortArray(windows, "ProcessName")
        
        ; 添加到列表视图
        for window in windows {
            appListView.Add("", window.ProcessName, window.Title, window.PID, window.ClassName)
        }
        
        ; 自动调整列宽
        appListView.ModifyCol(1, "AutoHdr")
        appListView.ModifyCol(2, "AutoHdr")
        appListView.ModifyCol(3, "AutoHdr")
        appListView.ModifyCol(4, "AutoHdr")
        
        statusText.Text := "已找到 " . windows.Length . " 个应用程序窗口"
        
    } catch Error as e {
        statusText.Text := "刷新失败: " . e.Message
        MsgBox("刷新应用程序列表失败:`n" . e.Message, "错误")
    }
}

; 检查是否为有效窗口
IsValidWindow(title, processName, className) {
    ; 过滤掉空标题
    if title = ""
        return false
    
    ; 过滤掉系统进程
    systemProcesses := ["dwm.exe", "winlogon.exe", "csrss.exe", "smss.exe", "wininit.exe"]
    for sysProc in systemProcesses {
        if processName = sysProc
            return false
    }
    
    ; 过滤掉一些系统窗口类
    systemClasses := ["Shell_TrayWnd", "DV2ControlHost", "MsgrIMEWindowClass", "SysShadow"]
    for sysClass in systemClasses {
        if className = sysClass
            return false
    }
    
    ; 过滤掉太短的标题
    if StrLen(title) < 3
        return false
    
    return true
}

; 检查窗口是否存在
IsWindow(hwnd) {
    return DllCall("IsWindow", "Ptr", hwnd)
}

; 数组排序函数
SortArray(arr, key) {
    ; 简单的冒泡排序
    n := arr.Length
    for i in Range(1, n-1) {
        for j in Range(1, n-i) {
            if arr[j][key] > arr[j+1][key] {
                temp := arr[j]
                arr[j] := arr[j+1]
                arr[j+1] := temp
            }
        }
    }
    return arr
}

; 扫描选中应用程序的控件
ScanSelectedApp() {
    global appListView, resultEdit, statusText, currentApp, scanResults, mainGui
    
    ; 获取选中的行
    selectedRow := appListView.GetNext()
    if !selectedRow {
        MsgBox("请先选择一个应用程序", "提示")
        return
    }
    
    ; 获取应用程序信息
    processName := appListView.GetText(selectedRow, 1)
    title := appListView.GetText(selectedRow, 2)
    pid := appListView.GetText(selectedRow, 3)
    
    currentApp := processName . " (" . title . ")"
    statusText.Text := "正在扫描 " . currentApp . " 的控件结构..."
    
    ; 获取扫描深度
    depthCombo := mainGui["DepthCombo"]
    maxDepth := GetMaxDepthFromSelection(depthCombo.Text)
    
    try {
        ; 通过PID获取窗口句柄
        hwnd := 0
        windowList := WinGetList()

        for testHwnd in windowList {
            if WinGetPID(testHwnd) = pid && WinGetTitle(testHwnd) = title {
                hwnd := testHwnd
                break
            }
        }
        
        if !hwnd {
            throw Error("无法找到目标窗口")
        }
        
        ; 获取UIA元素
        rootElement := UIA.ElementFromHandle(hwnd)
        if !rootElement {
            throw Error("无法获取UIA元素")
        }
        
        ; 开始扫描
        scanResults := "=== " . currentApp . " UI控件树结构 ===`n"
        scanResults .= "扫描时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        scanResults .= "窗口句柄: " . hwnd . "`n"
        scanResults .= "扫描深度: " . depthCombo.Text . "`n"
        scanResults .= "=" . StrRepeat("=", 50) . "`n`n"
        
        ; 递归扫描控件树
        ScanElementTree(rootElement, 0, maxDepth)
        
        ; 显示结果
        resultEdit.Text := scanResults
        statusText.Text := "扫描完成 - " . currentApp
        
    } catch Error as e {
        statusText.Text := "扫描失败: " . e.Message
        MsgBox("扫描控件失败:`n" . e.Message, "错误")
    }
}

; 获取最大扫描深度
GetMaxDepthFromSelection(selection) {
    if InStr(selection, "浅层")
        return 3
    else if InStr(selection, "中等")
        return 5
    else if InStr(selection, "深层")
        return 8
    else
        return -1 ; 无限深度
}

; 递归扫描元素树
ScanElementTree(element, depth, maxDepth) {
    global scanResults
    
    ; 检查深度限制
    if maxDepth > 0 && depth >= maxDepth
        return
    
    try {
        ; 获取元素基本信息
        elementInfo := GetElementInfo(element)
        
        ; 构建缩进
        indent := StrRepeat("  ", depth)
        
        ; 添加到结果
        scanResults .= indent . "├─ " . elementInfo.DisplayText . "`n"
        
        ; 如果有详细信息，添加属性行
        if elementInfo.Details != "" {
            detailLines := StrSplit(elementInfo.Details, "`n")
            for line in detailLines {
                if Trim(line) != "" {
                    scanResults .= indent . "│  " . line . "`n"
                }
            }
        }
        
        ; 递归扫描子元素
        try {
            children := element.GetChildren()
            if children && children.Length > 0 {
                for child in children {
                    ScanElementTree(child, depth + 1, maxDepth)
                }
            }
        } catch {
            ; 忽略获取子元素失败的情况
        }
        
    } catch {
        ; 忽略获取元素信息失败的情况
        indent := StrRepeat("  ", depth)
        scanResults .= indent . "├─ [无法访问的元素]`n"
    }
}

; 获取元素详细信息
GetElementInfo(element) {
    info := {DisplayText: "", Details: ""}
    
    try {
        ; 基本信息
        name := element.Name ? element.Name : "[无名称]"
        type := element.LocalizedType ? element.LocalizedType : "[无类型]"
        className := element.ClassName ? element.ClassName : ""
        automationId := element.AutomationId ? element.AutomationId : ""
        value := element.Value ? element.Value : ""
        
        ; 构建显示文本
        info.DisplayText := type . ": " . name
        
        ; 构建详细信息
        details := []
        
        if className != ""
            details.Push("类名: " . className)
        
        if automationId != ""
            details.Push("自动化ID: " . automationId)
        
        if value != "" && StrLen(value) < 100
            details.Push("值: " . value)
        
        ; 获取位置信息
        try {
            rect := element.BoundingRectangle
            if rect {
                details.Push("位置: (" . rect.x . "," . rect.y . ") 大小: " . rect.width . "x" . rect.height)
            }
        } catch {
            ; 忽略位置获取失败
        }
        
        ; 获取状态信息
        try {
            if element.IsEnabled != ""
                details.Push("启用: " . (element.IsEnabled ? "是" : "否"))
            
            if element.IsVisible != ""
                details.Push("可见: " . (element.IsVisible ? "是" : "否"))
        } catch {
            ; 忽略状态获取失败
        }
        
        ; 获取支持的模式
        try {
            patterns := element.GetSupportedPatterns()
            if patterns && patterns.Length > 0 {
                patternNames := []
                for pattern in patterns {
                    patternNames.Push(pattern)
                }
                details.Push("支持模式: " . Join(patternNames, ", "))
            }
        } catch {
            ; 忽略模式获取失败
        }
        
        info.Details := Join(details, "`n")
        
    } catch {
        info.DisplayText := "[获取信息失败]"
        info.Details := ""
    }
    
    return info
}

; 字符串重复函数
StrRepeat(str, count) {
    result := ""
    Loop count {
        result .= str
    }
    return result
}

; 数组连接函数
Join(arr, separator) {
    result := ""
    for i, item in arr {
        if i > 1
            result .= separator
        result .= item
    }
    return result
}

; 导出扫描结果
ExportResults() {
    global scanResults, currentApp, statusText
    
    if scanResults = "" {
        MsgBox("没有可导出的结果，请先扫描应用程序", "提示")
        return
    }
    
    try {
        ; 生成文件名
        safeAppName := RegExReplace(currentApp, '[<>:"/\\|?*]', "_")
        fileName := "UI_Controls_" . safeAppName . "_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
        
        ; 写入文件
        FileAppend(scanResults, fileName, "UTF-8")
        
        statusText.Text := "结果已导出到: " . fileName
        MsgBox("扫描结果已导出到:`n" . fileName, "导出成功")
        
    } catch Error as e {
        statusText.Text := "导出失败: " . e.Message
        MsgBox("导出失败:`n" . e.Message, "错误")
    }
}

; 清空结果
ClearResults() {
    global resultEdit, scanResults, statusText
    
    resultEdit.Text := "请选择应用程序并点击'扫描控件'开始分析..."
    scanResults := ""
    statusText.Text := "结果已清空"
}

; 创建Range函数（AHK v2兼容）
Range(start, end) {
    arr := []
    Loop end - start + 1 {
        arr.Push(start + A_Index - 1)
    }
    return arr
}

; ===== 高级功能扩展 =====

; 搜索特定控件
SearchControls() {
    global scanResults, resultEdit, statusText

    if scanResults = "" {
        MsgBox("请先扫描应用程序控件", "提示")
        return
    }

    ; 获取搜索关键词
    searchTerm := InputBox("请输入要搜索的控件名称或类型:", "搜索控件").Value
    if searchTerm = ""
        return

    ; 在结果中搜索
    lines := StrSplit(scanResults, "`n")
    matchedLines := []

    for line in lines {
        if InStr(line, searchTerm) {
            matchedLines.Push(line)
        }
    }

    if matchedLines.Length = 0 {
        MsgBox("未找到包含 '" . searchTerm . "' 的控件", "搜索结果")
        return
    }

    ; 显示搜索结果
    searchResult := "=== 搜索结果: " . searchTerm . " ===`n"
    searchResult .= "找到 " . matchedLines.Length . " 个匹配项:`n`n"

    for line in matchedLines {
        searchResult .= line . "`n"
    }

    ; 创建搜索结果窗口
    ShowSearchResults(searchResult, searchTerm)
}

; 显示搜索结果窗口
ShowSearchResults(results, searchTerm) {
    searchGui := Gui("+Resize", "搜索结果 - " . searchTerm)
    searchGui.SetFont("s9", "Microsoft YaHei")

    searchGui.Add("Text", "x10 y10", "搜索结果:")
    searchEdit := searchGui.Add("Edit", "x10 y35 w500 h300 +VScroll +HScroll +ReadOnly", results)
    searchEdit.SetFont("s8", "Consolas")

    searchGui.Add("Button", "x10 y345 w100 h30", "复制结果").OnEvent("Click", (*) => A_Clipboard := results)
    searchGui.Add("Button", "x120 y345 w100 h30", "关闭").OnEvent("Click", (*) => searchGui.Close())

    searchGui.Show("w520 h385")
}

; 实时监控模式
StartRealTimeMonitoring() {
    global statusText

    ; 获取当前选中的应用程序
    selectedRow := appListView.GetNext()
    if !selectedRow {
        MsgBox("请先选择一个应用程序", "提示")
        return
    }

    processName := appListView.GetText(selectedRow, 1)
    title := appListView.GetText(selectedRow, 2)
    pid := appListView.GetText(selectedRow, 3)

    ; 创建实时监控窗口
    monitorGui := Gui("+Resize +AlwaysOnTop", "实时监控 - " . processName)
    monitorGui.SetFont("s9", "Microsoft YaHei")

    monitorGui.Add("Text", "x10 y10", "监控目标: " . processName . " (" . title . ")")
    monitorGui.Add("Text", "x10 y35", "鼠标位置控件信息:")

    monitorEdit := monitorGui.Add("Edit", "x10 y60 w400 h200 +VScroll +ReadOnly")
    monitorEdit.SetFont("s8", "Consolas")

    monitorGui.Add("Button", "x10 y270 w80 h25", "开始监控").OnEvent("Click", (*) => StartMonitoring(monitorEdit, pid))
    monitorGui.Add("Button", "x100 y270 w80 h25", "停止监控").OnEvent("Click", (*) => StopMonitoring())
    monitorGui.Add("Button", "x190 y270 w80 h25", "关闭").OnEvent("Click", (*) => monitorGui.Close())

    monitorGui.Show("w420 h305")
}

; 开始监控
StartMonitoring(editControl, targetPid) {
    global monitoringActive := true

    ; 启动监控定时器
    SetTimer(() => UpdateMouseElementInfo(editControl, targetPid), 200)
}

; 停止监控
StopMonitoring() {
    global monitoringActive := false
    SetTimer(UpdateMouseElementInfo, 0)
}

; 更新鼠标位置的元素信息
UpdateMouseElementInfo(editControl, targetPid) {
    global monitoringActive

    if !monitoringActive
        return

    try {
        ; 获取鼠标位置
        MouseGetPos(&x, &y, &hwnd)

        ; 检查是否在目标应用程序窗口内
        if WinGetPID(hwnd) != targetPid
            return

        ; 获取鼠标位置的UIA元素
        element := UIA.ElementFromPoint(x, y)
        if !element
            return

        ; 获取元素信息
        info := GetElementInfo(element)

        ; 构建显示文本
        displayText := "位置: (" . x . "," . y . ")`n"
        displayText .= "元素: " . info.DisplayText . "`n"
        displayText .= "详细信息:`n" . info.Details . "`n"
        displayText .= StrRepeat("-", 40) . "`n"

        ; 更新显示（保持最近5条记录）
        currentText := editControl.Text
        lines := StrSplit(currentText, "`n")

        ; 限制行数
        if lines.Length > 50 {
            newLines := []
            for i in Range(lines.Length - 30, lines.Length) {
                newLines.Push(lines[i])
            }
            currentText := Join(newLines, "`n")
        }

        editControl.Text := displayText . currentText

    } catch {
        ; 忽略监控错误
    }
}

; 生成控件统计报告
GenerateStatisticsReport() {
    global scanResults, currentApp

    if scanResults = "" {
        MsgBox("请先扫描应用程序控件", "提示")
        return
    }

    ; 分析控件类型统计
    lines := StrSplit(scanResults, "`n")
    controlTypes := Map()
    totalControls := 0

    for line in lines {
        if InStr(line, "├─") {
            totalControls++

            ; 提取控件类型
            if RegExMatch(line, "├─\s*([^:]+):", &match) {
                controlType := Trim(match[1])
                if controlTypes.Has(controlType) {
                    controlTypes[controlType] := controlTypes[controlType] + 1
                } else {
                    controlTypes[controlType] := 1
                }
            }
        }
    }

    ; 生成统计报告
    report := "=== " . currentApp . " 控件统计报告 ===`n"
    report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
    report .= "总控件数量: " . totalControls . "`n`n"

    report .= "=== 控件类型分布 ===`n"

    ; 转换为数组并排序
    typeArray := []
    for type, count in controlTypes {
        typeArray.Push({Type: type, Count: count})
    }

    ; 按数量排序
    typeArray := SortArrayByCount(typeArray)

    for item in typeArray {
        percentage := Round(item.Count / totalControls * 100, 1)
        report .= item.Type . ": " . item.Count . " 个 (" . percentage . "%)`n"
    }

    ; 显示统计报告
    ShowStatisticsReport(report)
}

; 按数量排序数组
SortArrayByCount(arr) {
    n := arr.Length
    for i in Range(1, n-1) {
        for j in Range(1, n-i) {
            if arr[j].Count < arr[j+1].Count {
                temp := arr[j]
                arr[j] := arr[j+1]
                arr[j+1] := temp
            }
        }
    }
    return arr
}

; 显示统计报告
ShowStatisticsReport(report) {
    statsGui := Gui("+Resize", "控件统计报告")
    statsGui.SetFont("s9", "Microsoft YaHei")

    statsGui.Add("Text", "x10 y10", "统计报告:")
    statsEdit := statsGui.Add("Edit", "x10 y35 w450 h300 +VScroll +ReadOnly", report)
    statsEdit.SetFont("s8", "Consolas")

    statsGui.Add("Button", "x10 y345 w100 h30", "导出报告").OnEvent("Click", (*) => ExportStatistics(report))
    statsGui.Add("Button", "x120 y345 w100 h30", "复制").OnEvent("Click", (*) => A_Clipboard := report)
    statsGui.Add("Button", "x230 y345 w100 h30", "关闭").OnEvent("Click", (*) => statsGui.Close())

    statsGui.Show("w470 h385")
}

; 导出统计报告
ExportStatistics(report) {
    try {
        fileName := "UI_Statistics_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"
        FileAppend(report, fileName, "UTF-8")
        MsgBox("统计报告已导出到:`n" . fileName, "导出成功")
    } catch Error as e {
        MsgBox("导出失败:`n" . e.Message, "错误")
    }
}

; 添加更多热键
F5::SearchControls()
F6::StartRealTimeMonitoring()
F7::GenerateStatisticsReport()
